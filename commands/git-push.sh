#!/bin/bash

# Git commit and push script
# Usage: ./commands/git-push.sh "Your commit message"

# Check if commit message is provided
if [ -z "$1" ]; then
    echo "Error: Please provide a commit message"
    echo "Usage: ./commands/git-push.sh \"Your commit message\""
    exit 1
fi

# Get current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Change to project directory
cd "$PROJECT_DIR"

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    echo "Error: Not in a git repository"
    exit 1
fi

# Show current status
echo "Current git status:"
git status --short

# Add all changes
echo "Adding all changes..."
git add .

# Commit with provided message
echo "Creating commit..."
git commit -m "$1

Generated by John @ YourDesign.co.za"

# Check if commit was successful
if [ $? -eq 0 ]; then
    echo "Commit successful. Pushing to origin..."
    git push origin main
    
    if [ $? -eq 0 ]; then
        echo "✅ Successfully pushed to GitHub!"
    else
        echo "❌ Failed to push to GitHub"
        exit 1
    fi
else
    echo "❌ Failed to create commit"
    exit 1
fi